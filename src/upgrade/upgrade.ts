import { downloadToFile } from '@/utils/download';
import * as vscode from 'vscode';
import { notify } from '@/notify';
import { isUpgrade, getCurVersion } from './check';
import {
  ARTIFACT_BASE_URL,
  CMD,
  CODEFACTORY_DOWNLOAD_URL,
  COMP_CODE_FACTORY,
  COMP_CODE_PLUGIN,
  DOWNLOAD_CACHE_DIR
} from '@/config';
import * as fse from 'fs-extra';
import { MENU_STATE } from '@/menu';
import { persistentEvent } from '@/telemetry';

export const CMD_PLUGIN_UPGRADE_TO_VERSION = `${CMD}.action.upgrade`;
export const CMD_CODE_FACTORY_UPGRADE_TO_VERSION = `${CMD}.action.code-factory.upgrade`;

export async function registerUpgrade(
  _context: vscode.ExtensionContext
): Promise<vscode.Disposable[]> {
  return [
    vscode.commands.registerCommand(CMD_PLUGIN_UPGRADE_TO_VERSION, (version, silent) =>
      upgrade(COMP_CODE_PLUGIN, version, silent)
    ),
    vscode.commands.registerCommand(CMD_CODE_FACTORY_UPGRADE_TO_VERSION, (version, silent) =>
      upgrade(COMP_CODE_FACTORY, version, silent)
    )
  ];
}

async function upgrade(component: string, version: string, silent: string) {
  if (!version) {
    console.error('Cannot upgrade: version is empty.');
    return;
  }
  try {
    await upgradePlugin(component, version);
    console.log('Upgraded agent:', version);
  } catch (e) {
    console.error('Upgrade failed:', e);
    if (!silent) {
      notify.whenPluginUpgradeFailed(component, version);
    }
  }

  if (!silent) {
    setTimeout(() => {
      notify.whenPluginUpgraded(component, version);
    }, 10);
  }
}

async function upgradePlugin(component: string, version: string) {
  if (component === COMP_CODE_PLUGIN && !isUpgrade('vscode', version)) {
    throw new Error(
      `Cannot upgrade: target version is not bigger than current: !(${version}`
    );
  }
  const startTime = performance.now();
  const curVersion = getCurVersion(component);
  const details = {
    component,
    timestamp: new Date(Date.now()),
    currentVersion: curVersion || '',
    targetVersion: version,
  };
  try {
    const fileUrl = await (component === COMP_CODE_PLUGIN
      ? downloadCodeBuddyPlugin(version)
      : downloadCodeFactoryPlugin(version));

    // https://chat.chehejia.com/chat/5c271b12-6966-4515-be73-47f3c73f339f
    const url = vscode.Uri.file(fileUrl);
    if (component !== COMP_CODE_FACTORY) {
      MENU_STATE.patch({
        status: 'upgrading',
      });
    }
    await vscode.commands.executeCommand(
      'workbench.extensions.installExtension',
      url
    );
    console.log(`Upgrading plugin to ${version}...`);
    // FIXME: Clean up the downloaded file.
    const endTime = performance.now();
    const event = persistentEvent({
      name: `copilot:${component}:upgrade`,
      du: endTime - startTime
    });
    event.end({ details: { ...details, status: 'success' } });
  } catch (e) {
    const endTime = performance.now();
    const event = persistentEvent({
      name: `copilot:${component}:upgrade`,
      du: endTime - startTime
    });
    event.end({ details: { ...details, status: 'failure', errMsg: `${e}` } });
  }
}

async function downloadCodeBuddyPlugin(version: string): Promise<string> {
  const packageName = `codebuddy-vscode-${version}.vsix`;
  const url = `${ARTIFACT_BASE_URL}/${COMP_CODE_PLUGIN}/${version}/${packageName}`;
  const targetUrl = `${DOWNLOAD_CACHE_DIR}/${packageName}`;

  console.log('Downloading plugin binary...', url, targetUrl);
  fse.ensureDirSync(DOWNLOAD_CACHE_DIR);

  await downloadToFile(url, targetUrl);
  console.log('Downloaded plugin binary to', targetUrl);
  return targetUrl;
}

async function downloadCodeFactoryPlugin(version: string): Promise<string> {
  const packageName = `codefactory-v${version}.vsix`;
  const url = `${CODEFACTORY_DOWNLOAD_URL}/${packageName}`;
  console.log(`Download CodeFactory vsix from ${url}`);

  const targetUrl = `${DOWNLOAD_CACHE_DIR}/${packageName}`;
  fse.ensureDirSync(DOWNLOAD_CACHE_DIR);

  await downloadToFile(url, targetUrl);
  return targetUrl;
}
