// The module 'vscode' contains the VS Code extensibility API
// Import the module and reference it with the alias vscode in your code below
import { Disposable, ExtensionContext, extensions, workspace } from 'vscode';
import { agent, initAgent } from '@/agent';
import { AuthStatus } from '@/agent/lsp-client';
import { initCommands } from './commands';
import { appEnv } from './judge_env';
import {
  getGlobalConf,
  onConfigChanged,
  PLUGIN_LABEL,
  VSCODE_CODE
} from './config';
import {
  initTelemetry,
  PersistentEvent,
  persistentEvent,
  telemetry
} from './telemetry';
import { initLocalStore, localStore } from './store';
import { compareVersions } from 'compare-versions';
import { initDocTracker } from './tracker/doc-tracker';
import { UserInstalledExtensionsTracker } from './tracker/user-installed-extensions-tracker';
import { enableTracker } from './tracker/index';
import { initChatPanel } from './chat';
import { initMenu } from './menu';
import { initLogChannel } from '@/log';
import { checkUpgrade, checkUpgradeAfterActivated, setCodeFactoryVersion } from '@/upgrade/check';
import { registerUpgrade } from '@/upgrade/upgrade';
import { registerEditCommands } from '@/edit/commands';
import { registerInlineChatCommands } from '@/inlineChat/commands';
import { onDidChangeTextEditorSelectionTriggerInlineChat } from '@/inlineChat/textEditorSelection';
import { channelFromVersion } from '@/utils';
import { initSuggestionsPanel } from '@/completion/panel';
import { initInlineCompletion } from '@/completion/inline';
import { initCodeLens } from '@/codelens';
// import { initTestingPanel } from '@/testing';
import { onDidChangeTextDocumentHandler } from '@/utils/vscode/workspace';
import { createCodeBuddyAPI } from './exports';
import * as os from 'os';
import * as vscode from 'vscode';
import { IDaasAuthProvider } from './auth/auth_provider';
import { UriHandlerImpl } from './handler/uriHandler';
import { initContextManager } from '@/context';

let APP_VERSION: string | undefined;
let APP_CHANNEL: string | undefined;

let CONSOLE_DEBUG: boolean | undefined;

export type PluginId =
  'alibaba_tongyi_lingma'
  | 'aminer_codegeex'
  | 'aws_toolkit'
  | 'baidu_comate'
  | 'codefuse_codefuse'
  | 'codium_codium'
  | 'github_copilot'
  | 'huawei_codearts_snap'
  | 'li_liep_licode'
  | 'tabnine_vscode'
  ;

// @ts-ignore
export const PLUGINS_INSTALLED: { [key: PluginId]: boolean } = {};
export let authProvider: IDaasAuthProvider;

const PLUGIN_CHECKLIST: {
  // @ts-ignore
  [key: PluginId]: string
} = {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  'alibaba_tongyi_lingma': 'Alibaba-Cloud.tongyi-lingma',
  // eslint-disable-next-line @typescript-eslint/naming-convention
  'aminer_codegeex': 'aminer.codegeex',
  // eslint-disable-next-line @typescript-eslint/naming-convention
  'aws_toolkit': 'AmazonWebServices.aws-toolkit-vscode',
  // eslint-disable-next-line @typescript-eslint/naming-convention
  'baidu_comate': 'BaiduComate.comate',
  // eslint-disable-next-line @typescript-eslint/naming-convention
  'codefuse_codefuse': 'codeFuse.CodeFuse',
  // eslint-disable-next-line @typescript-eslint/naming-convention
  'codium_codium': 'Codium.codium',
  // eslint-disable-next-line @typescript-eslint/naming-convention
  'github_copilot': 'GitHub.copilot',
  // eslint-disable-next-line @typescript-eslint/naming-convention
  'huawei_codearts_snap': 'HuaweiCloud.vscode-codebot',
  // eslint-disable-next-line @typescript-eslint/naming-convention
  'li_liep_licode': 'liep.licode-ai',
  // eslint-disable-next-line @typescript-eslint/naming-convention
  'tabnine_vscode': 'TabNine.tabnine-vscode'
};

export function consoleDebug(...data: any[]) {
  if (CONSOLE_DEBUG !== true) {
    return;
  }
  console.log.apply(null, data);
}

export function appVersion(): string {
  return APP_VERSION || 'unknown';
}

export function appChannel(): string {
  return APP_CHANNEL || 'unknown';
}

export async function currentUser(): Promise<string> {
  let authStatus: AuthStatus = { authorized: false };
  try {
    authStatus = await agent().authStatus();
    console.log('authStatus', authStatus);
  } catch (e: any) {
    console.log(e.message);
  }
  if (authStatus.authorized) {
    const email = authStatus.account?.email!;
    return email.split('@')[0];
  }
  return os.userInfo().username;
}

function reloadConsoleDebugOption() {
  CONSOLE_DEBUG = getGlobalConf().get('advance.consoleDebugEnabled', false);
}

export async function activate (context: ExtensionContext) {
  // /Users/<USER>/wsp/coding-copilot/codebuddy-vscode/codebuddy-vscode-1.6.1-dev.23.vsix
}

// this method is called when your extension is activated
// your extension is activated the very first time the command is executed
// export async function activate(context: ExtensionContext) {
//   console.log('CodeBuddy appEnv: ', appEnv());
//   const pkg = context.extension.packageJSON || {};
//   APP_VERSION = pkg.version;
//   APP_CHANNEL = channelFromVersion(APP_VERSION);

//   reloadConsoleDebugOption();

//   const uriHandler = new UriHandlerImpl();
//   authProvider = new IDaasAuthProvider(context, uriHandler);
//   uriHandler.registerHandler('/ping', { async handle() { } });
//   context.subscriptions.push(
//     vscode.window.registerUriHandler(uriHandler),
//     authProvider
//   );

//   const activateEvent = persistentEvent({
//     name: 'plugin:activated'
//   });

//   const collectSubscriptions =
//     (d: Disposable[] | void) => d && context.subscriptions.push(...d);

//   (
//     await Promise.all([
//       initLogChannel(context),
//       initLocalStore(context),
//       initTelemetry(context),
//       initCodeLens(context),
//       enableTracker(),
//       (async () => {
//         Object.keys(PLUGIN_CHECKLIST).forEach(key => {
//           // @ts-ignore
//           PLUGINS_INSTALLED[key] = !!extensions.getExtension(PLUGIN_CHECKLIST[key]);
//         });
//         console.log('PLUGINS_INSTALLED: ', PLUGINS_INSTALLED);
//       })()
//     ])
//   ).forEach(collectSubscriptions);

//   // Initialize context manager
//   collectSubscriptions(initContextManager());

//   (
//     await Promise.all([
//       registerUpgrade(context),
//       registerEditCommands(context),
//       registerInlineChatCommands(context),
//       initAgent(context),
//       initCommands(context),
//       initInlineCompletion(context),
//       initSuggestionsPanel(context),
//       initDocTracker(context),
//       // initTestingPanel(context)
//     ])
//   ).forEach(collectSubscriptions);

//   // install codefactory
//   await initCodeFactory();

//   (await Promise.all([
//     initChatPanel(context), // Depends on agent
//     initMenu(context), // Depends on agent
//   ])).forEach(collectSubscriptions);

//   // Async active agent
//   agent().afterActivated();

//   console.debug(`[${PLUGIN_LABEL}] Activated: ${new Date()}`);
//   await afterActivated(context, activateEvent);

//   context.subscriptions.push(
//     onConfigChanged(_ =>
//       reloadConsoleDebugOption()
//     ),
//     ...await checkUpgradeAfterActivated(),
//     workspace.onDidChangeTextDocument(onDidChangeTextDocumentHandler),
//   );
//   UserInstalledExtensionsTracker.instance.scheduleReport();
//   onDidChangeTextEditorSelectionTriggerInlineChat(context);
//   return createCodeBuddyAPI();
// }

// this method is called when your extension is deactivated
// noinspection JSUnusedGlobalSymbols
export function deactivate() {
  agent().stop();
  console.debug(`Deactivated ${PLUGIN_LABEL} extension: ${new Date()}`);
  telemetry().offer({
    name: 'plugin:deactivated'
  });
}

type InstallType = 'first-install' | 'upgrade' | 'downgrade' | undefined;

async function afterActivated(
  _context: ExtensionContext,
  event: PersistentEvent
) {
  const version = appVersion();
  const versionMapping = localStore.data.histories[VSCODE_CODE].versions;
  const versions = Object.keys(versionMapping).sort((a, b) =>
    compareVersions(b, a)
  );
  const lastVersion = versions.length === 0 ? null : versions[0];

  let installType: InstallType;
  if (versionMapping[version]) {
    installType = undefined;
  } else if (lastVersion === null) {
    installType = 'first-install';
  } else {
    installType =
      compareVersions(version, lastVersion) > 0 ? 'upgrade' : 'downgrade';
  }

  // FIXME: must waiting for agent ready
  event.end({
    details: {
      installType,
      lastVersion
    }
  });

  if (installType === undefined) {
    return;
  }

  console.log('Install type: ', installType, version, versions);
  versionMapping[version] = { time: Date.now() };
  await localStore.save();
}
async function initCodeFactory(): Promise<void> {
  const codefactory = vscode.extensions.getExtension('LiAuto.code-factory');
  if (codefactory) {
    // get version from package.json
    const codefactoryVersion = codefactory.packageJSON.version;
    setCodeFactoryVersion(codefactoryVersion);
    return;
  }

  // if not, try to install
  try {
    await checkUpgrade('codefactory', true);
  } catch (e) {
    console.log('[CodeFactory] codefactory安装失败');
  }
}
