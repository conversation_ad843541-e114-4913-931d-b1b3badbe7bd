// The module 'vscode' contains the VS Code extensibility API
// Import the module and reference it with the alias vscode in your code below
import { Disposable, ExtensionContext, extensions, workspace } from 'vscode';
import { agent, initAgent } from '@/agent';
import { AuthStatus } from '@/agent/lsp-client';
import { initCommands } from './commands';
import { appEnv } from './judge_env';
import {
  getGlobalConf,
  onConfigChanged,
  PLUGIN_LABEL,
  VSCODE_CODE
} from './config';
import {
  initTelemetry,
  PersistentEvent,
  persistentEvent,
  telemetry
} from './telemetry';
import { initLocalStore, localStore } from './store';
import { compareVersions } from 'compare-versions';
import { initDocTracker } from './tracker/doc-tracker';
import { UserInstalledExtensionsTracker } from './tracker/user-installed-extensions-tracker';
import { enableTracker } from './tracker/index';
import { initChatPanel } from './chat';
import { initMenu } from './menu';
import { initLogChannel } from '@/log';
import { checkUpgrade, checkUpgradeAfterActivated, setCodeFactoryVersion } from '@/upgrade/check';
import { registerUpgrade } from '@/upgrade/upgrade';
import { registerEditCommands } from '@/edit/commands';
import { registerInlineChatCommands } from '@/inlineChat/commands';
import { onDidChangeTextEditorSelectionTriggerInlineChat } from '@/inlineChat/textEditorSelection';
import { channelFromVersion } from '@/utils';
import { initSuggestionsPanel } from '@/completion/panel';
import { initInlineCompletion } from '@/completion/inline';
import { initCodeLens } from '@/codelens';
// import { initTestingPanel } from '@/testing';
import { onDidChangeTextDocumentHandler } from '@/utils/vscode/workspace';
import { createCodeBuddyAPI } from './exports';
import * as os from 'os';
import * as vscode from 'vscode';
import { IDaasAuthProvider } from './auth/auth_provider';
import { UriHandlerImpl } from './handler/uriHandler';
import { initContextManager } from '@/context';

let APP_VERSION: string | undefined;
let APP_CHANNEL: string | undefined;

let CONSOLE_DEBUG: boolean | undefined;

export type PluginId =
  'alibaba_tongyi_lingma'
  | 'aminer_codegeex'
  | 'aws_toolkit'
  | 'baidu_comate'
  | 'codefuse_codefuse'
  | 'codium_codium'
  | 'github_copilot'
  | 'huawei_codearts_snap'
  | 'li_liep_licode'
  | 'tabnine_vscode'
  ;

// @ts-ignore
export const PLUGINS_INSTALLED: { [key: PluginId]: boolean } = {};
export let authProvider: IDaasAuthProvider;

const PLUGIN_CHECKLIST: {
  // @ts-ignore
  [key: PluginId]: string
} = {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  'alibaba_tongyi_lingma': 'Alibaba-Cloud.tongyi-lingma',
  // eslint-disable-next-line @typescript-eslint/naming-convention
  'aminer_codegeex': 'aminer.codegeex',
  // eslint-disable-next-line @typescript-eslint/naming-convention
  'aws_toolkit': 'AmazonWebServices.aws-toolkit-vscode',
  // eslint-disable-next-line @typescript-eslint/naming-convention
  'baidu_comate': 'BaiduComate.comate',
  // eslint-disable-next-line @typescript-eslint/naming-convention
  'codefuse_codefuse': 'codeFuse.CodeFuse',
  // eslint-disable-next-line @typescript-eslint/naming-convention
  'codium_codium': 'Codium.codium',
  // eslint-disable-next-line @typescript-eslint/naming-convention
  'github_copilot': 'GitHub.copilot',
  // eslint-disable-next-line @typescript-eslint/naming-convention
  'huawei_codearts_snap': 'HuaweiCloud.vscode-codebot',
  // eslint-disable-next-line @typescript-eslint/naming-convention
  'li_liep_licode': 'liep.licode-ai',
  // eslint-disable-next-line @typescript-eslint/naming-convention
  'tabnine_vscode': 'TabNine.tabnine-vscode'
};

export function consoleDebug(...data: any[]) {
  if (CONSOLE_DEBUG !== true) {
    return;
  }
  console.log.apply(null, data);
}

export function appVersion(): string {
  return APP_VERSION || 'unknown';
}

export function appChannel(): string {
  return APP_CHANNEL || 'unknown';
}

export async function currentUser(): Promise<string> {
  let authStatus: AuthStatus = { authorized: false };
  try {
    authStatus = await agent().authStatus();
    console.log('authStatus', authStatus);
  } catch (e: any) {
    console.log(e.message);
  }
  if (authStatus.authorized) {
    const email = authStatus.account?.email!;
    return email.split('@')[0];
  }
  return os.userInfo().username;
}

function reloadConsoleDebugOption() {
  CONSOLE_DEBUG = getGlobalConf().get('advance.consoleDebugEnabled', false);
}

export async function activate(context: ExtensionContext) {
  console.log('CodeBuddy Extension Activation Started');

  // 并发测试 codebuddy-vscode 安装程序
  await runConcurrentInstallationTest();
}

/**
 * 并发测试 codebuddy-vscode 安装程序
 * 测试多个并发安装请求的处理能力和错误处理机制
 */
async function runConcurrentInstallationTest(): Promise<void> {
  console.log('开始并发安装测试...');

  // 测试用的 VSIX 文件路径（这些应该是实际存在的文件）
  const testVsixFiles = [
    '/Users/<USER>/wsp/coding-copilot/codebuddy-vscode/codebuddy-vscode-1.6.1-dev.23.vsix',
    '/Users/<USER>/wsp/coding-copilot/codebuddy-vscode/vscode-li-codebuddy-1.2.0-dev.vsix',
    '/Users/<USER>/wsp/coding-copilot/codebuddy-vscode/codefactory-v0.5.0-alpha.4.vsix',
    // 可以添加更多测试文件路径
  ];

  // 并发测试配置
  const concurrentCount = 3; // 并发数量
  const testResults: Array<{
    id: number;
    success: boolean;
    error?: string;
    duration: number;
  }> = [];

  // 创建并发安装任务
  const installTasks = Array.from({ length: concurrentCount }, (_, index) =>
    testInstallExtension(index + 1, testVsixFiles[0] || testVsixFiles[index % testVsixFiles.length])
  );

  try {
    // 执行并发安装测试
    const startTime = Date.now();
    const results = await Promise.allSettled(installTasks);
    const totalDuration = Date.now() - startTime;

    // 处理测试结果
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        testResults.push(result.value);
      } else {
        testResults.push({
          id: index + 1,
          success: false,
          error: result.reason?.message || '未知错误',
          duration: 0
        });
      }
    });

    // 输出测试结果
    console.log('=== 并发安装测试结果 ===');
    console.log(`总耗时: ${totalDuration}ms`);
    console.log(`并发数量: ${concurrentCount}`);

    let successCount = 0;
    let failureCount = 0;

    testResults.forEach(result => {
      const status = result.success ? '✅ 成功' : '❌ 失败';
      console.log(`任务 ${result.id}: ${status} (耗时: ${result.duration}ms)`);

      if (result.error) {
        console.error(`  错误信息: ${result.error}`);
      }

      if (result.success) {
        successCount++;
      } else {
        failureCount++;
      }
    });

    console.log(`成功: ${successCount}, 失败: ${failureCount}`);

    // 测试扩展状态检查
    await testExtensionStatus();

  } catch (error) {
    console.error('并发安装测试过程中发生错误:', error);
  }
}

/**
 * 测试单个扩展安装
 */
async function testInstallExtension(taskId: number, vsixPath: string): Promise<{
  id: number;
  success: boolean;
  error?: string;
  duration: number;
}> {
  const startTime = Date.now();

  try {
    console.log(`任务 ${taskId}: 开始安装扩展 ${vsixPath}`);

    // 检查文件是否存在
    const fs = require('fs');
    if (!fs.existsSync(vsixPath)) {
      throw new Error(`VSIX 文件不存在: ${vsixPath}`);
    }

    const url = vscode.Uri.file(vsixPath);

    // 执行安装命令
    await vscode.commands.executeCommand(
      'workbench.extensions.installExtension',
      url
    );

    const duration = Date.now() - startTime;
    console.log(`任务 ${taskId}: 安装成功，耗时 ${duration}ms`);

    return {
      id: taskId,
      success: true,
      duration
    };

  } catch (error: any) {
    const duration = Date.now() - startTime;
    const errorMessage = error?.message || '未知错误';

    console.error(`任务 ${taskId}: 安装失败，耗时 ${duration}ms`);
    console.error(`任务 ${taskId}: 错误详情:`, error);

    // 分析常见错误类型
    let errorType = '未知错误';
    if (errorMessage.includes('Please restart VS Code')) {
      errorType = '需要重启 VS Code';
    } else if (errorMessage.includes('already installed')) {
      errorType = '扩展已安装';
    } else if (errorMessage.includes('not found')) {
      errorType = '文件未找到';
    } else if (errorMessage.includes('Internal')) {
      errorType = 'VS Code 内部错误';
    }

    return {
      id: taskId,
      success: false,
      error: `${errorType}: ${errorMessage}`,
      duration
    };
  }
}

/**
 * 测试扩展状态检查
 */
async function testExtensionStatus(): Promise<void> {
  console.log('\n=== 扩展状态检查 ===');

  try {
    // 检查 CodeBuddy 扩展状态
    const codeBuddyExtension = extensions.getExtension('LiAuto.vscode-li-codebuddy');
    if (codeBuddyExtension) {
      console.log('✅ CodeBuddy 扩展已安装');
      console.log(`   版本: ${codeBuddyExtension.packageJSON.version}`);
      console.log(`   激活状态: ${codeBuddyExtension.isActive ? '已激活' : '未激活'}`);
    } else {
      console.log('❌ CodeBuddy 扩展未找到');
    }

    // 检查 CodeFactory 扩展状态
    const codeFactoryExtension = extensions.getExtension('LiAuto.code-factory');
    if (codeFactoryExtension) {
      console.log('✅ CodeFactory 扩展已安装');
      console.log(`   版本: ${codeFactoryExtension.packageJSON.version}`);
      console.log(`   激活状态: ${codeFactoryExtension.isActive ? '已激活' : '未激活'}`);
    } else {
      console.log('❌ CodeFactory 扩展未找到');
    }

    // 检查其他相关插件
    console.log('\n=== 相关插件检查 ===');
    Object.entries(PLUGIN_CHECKLIST).forEach(([key, extensionId]) => {
      const ext = extensions.getExtension(extensionId as string);
      const status = ext ? '✅ 已安装' : '❌ 未安装';
      console.log(`${key}: ${status}`);
      if (ext) {
        console.log(`   ID: ${extensionId}`);
        console.log(`   版本: ${ext.packageJSON.version}`);
      }
    });

  } catch (error) {
    console.error('扩展状态检查失败:', error);
  }
}

// this method is called when your extension is activated
// your extension is activated the very first time the command is executed
// export async function activate(context: ExtensionContext) {
//   console.log('CodeBuddy appEnv: ', appEnv());
//   const pkg = context.extension.packageJSON || {};
//   APP_VERSION = pkg.version;
//   APP_CHANNEL = channelFromVersion(APP_VERSION);

//   reloadConsoleDebugOption();

//   const uriHandler = new UriHandlerImpl();
//   authProvider = new IDaasAuthProvider(context, uriHandler);
//   uriHandler.registerHandler('/ping', { async handle() { } });
//   context.subscriptions.push(
//     vscode.window.registerUriHandler(uriHandler),
//     authProvider
//   );

//   const activateEvent = persistentEvent({
//     name: 'plugin:activated'
//   });

//   const collectSubscriptions =
//     (d: Disposable[] | void) => d && context.subscriptions.push(...d);

//   (
//     await Promise.all([
//       initLogChannel(context),
//       initLocalStore(context),
//       initTelemetry(context),
//       initCodeLens(context),
//       enableTracker(),
//       (async () => {
//         Object.keys(PLUGIN_CHECKLIST).forEach(key => {
//           // @ts-ignore
//           PLUGINS_INSTALLED[key] = !!extensions.getExtension(PLUGIN_CHECKLIST[key]);
//         });
//         console.log('PLUGINS_INSTALLED: ', PLUGINS_INSTALLED);
//       })()
//     ])
//   ).forEach(collectSubscriptions);

//   // Initialize context manager
//   collectSubscriptions(initContextManager());

//   (
//     await Promise.all([
//       registerUpgrade(context),
//       registerEditCommands(context),
//       registerInlineChatCommands(context),
//       initAgent(context),
//       initCommands(context),
//       initInlineCompletion(context),
//       initSuggestionsPanel(context),
//       initDocTracker(context),
//       // initTestingPanel(context)
//     ])
//   ).forEach(collectSubscriptions);

//   // install codefactory
//   await initCodeFactory();

//   (await Promise.all([
//     initChatPanel(context), // Depends on agent
//     initMenu(context), // Depends on agent
//   ])).forEach(collectSubscriptions);

//   // Async active agent
//   agent().afterActivated();

//   console.debug(`[${PLUGIN_LABEL}] Activated: ${new Date()}`);
//   await afterActivated(context, activateEvent);

//   context.subscriptions.push(
//     onConfigChanged(_ =>
//       reloadConsoleDebugOption()
//     ),
//     ...await checkUpgradeAfterActivated(),
//     workspace.onDidChangeTextDocument(onDidChangeTextDocumentHandler),
//   );
//   UserInstalledExtensionsTracker.instance.scheduleReport();
//   onDidChangeTextEditorSelectionTriggerInlineChat(context);
//   return createCodeBuddyAPI();
// }

// this method is called when your extension is deactivated
// noinspection JSUnusedGlobalSymbols
export function deactivate() {
  agent().stop();
  console.debug(`Deactivated ${PLUGIN_LABEL} extension: ${new Date()}`);
  telemetry().offer({
    name: 'plugin:deactivated'
  });
}

type InstallType = 'first-install' | 'upgrade' | 'downgrade' | undefined;

async function afterActivated(
  _context: ExtensionContext,
  event: PersistentEvent
) {
  const version = appVersion();
  const versionMapping = localStore.data.histories[VSCODE_CODE].versions;
  const versions = Object.keys(versionMapping).sort((a, b) =>
    compareVersions(b, a)
  );
  const lastVersion = versions.length === 0 ? null : versions[0];

  let installType: InstallType;
  if (versionMapping[version]) {
    installType = undefined;
  } else if (lastVersion === null) {
    installType = 'first-install';
  } else {
    installType =
      compareVersions(version, lastVersion) > 0 ? 'upgrade' : 'downgrade';
  }

  // FIXME: must waiting for agent ready
  event.end({
    details: {
      installType,
      lastVersion
    }
  });

  if (installType === undefined) {
    return;
  }

  console.log('Install type: ', installType, version, versions);
  versionMapping[version] = { time: Date.now() };
  await localStore.save();
}
async function initCodeFactory(): Promise<void> {
  const codefactory = vscode.extensions.getExtension('LiAuto.code-factory');
  if (codefactory) {
    // get version from package.json
    const codefactoryVersion = codefactory.packageJSON.version;
    setCodeFactoryVersion(codefactoryVersion);
    return;
  }

  // if not, try to install
  try {
    await checkUpgrade('codefactory', true);
  } catch (e) {
    console.log('[CodeFactory] codefactory安装失败');
  }
}
